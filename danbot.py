from pack import CARD, PURE_SEQUENCE, IMPURE_SEQUENCE, SET
from bot import BOT_INTERFACE
import random

class BOT(BOT_INTERFACE):
	def __init__(self, name: str) -> None:
		super().__init__(name)

	def nextMove(self, topCard: CARD) -> str:
		if len(self.hand) == 13 and random.random() < 0.1:
			return "check"

		if self._is_card_useful(topCard):
			return "pick"
		else:
			return "draw"

	def takeCard(self) -> CARD:
		if not self.hand:
			return CARD("Hearts", "2")

		card_to_discard = random.choice(self.hand)
		self.hand.remove(card_to_discard)
		return card_to_discard

	def checkRummy(self) -> list[PURE_SEQUENCE | IMPURE_SEQUENCE | SET]:
		sequences_and_sets = []
		remaining_cards = self.hand.copy()

		pure_seq = self._try_form_pure_sequence(remaining_cards)
		if pure_seq:
			sequences_and_sets.append(pure_seq)
			for card in pure_seq.cards:
				if card in remaining_cards:
					remaining_cards.remove(card)

		while len(remaining_cards) >= 3:
			card_set = self._try_form_set(remaining_cards)
			if card_set:
				sequences_and_sets.append(card_set)
				for card in card_set.cards:
					if card in remaining_cards:
						remaining_cards.remove(card)
			else:
				impure_seq = self._try_form_impure_sequence(remaining_cards)
				if impure_seq:
					sequences_and_sets.append(impure_seq)
					for card in impure_seq.cards:
						if card in remaining_cards:
							remaining_cards.remove(card)
				else:
					break

		return sequences_and_sets

	def _is_card_useful(self, card: CARD) -> bool:
		if card.suit == "Joker":
			return True

		same_rank_count = sum(1 for c in self.hand if c.rank == card.rank and c.suit != "Joker")
		if same_rank_count >= 1:
			return True

		same_suit_cards = [c for c in self.hand if c.suit == card.suit and c.suit != "Joker"]
		if len(same_suit_cards) >= 2:
			return True

		return random.random() < 0.3

	def _try_form_pure_sequence(self, cards: list[CARD]) -> PURE_SEQUENCE | None:
		suits = {}
		for card in cards:
			if card.suit != "Joker":
				if card.suit not in suits:
					suits[card.suit] = []
				suits[card.suit].append(card)

		for suit, suit_cards in suits.items():
			if len(suit_cards) >= 3:
				rank_values = {"Ace": 1, "2": 2, "3": 3, "4": 4, "5": 5, "6": 6, "7": 7,
							  "8": 8, "9": 9, "10": 10, "Jack": 11, "Queen": 12, "King": 13}

				suit_cards.sort(key=lambda x: rank_values.get(x.rank, 0))

				for i in range(len(suit_cards) - 2):
					seq_cards = suit_cards[i:i+3]
					pure_seq = PURE_SEQUENCE(seq_cards)
					return pure_seq

		return None

	def _try_form_set(self, cards: list[CARD]) -> SET | None:
		ranks = {}
		for card in cards:
			if card.suit != "Joker":
				if card.rank not in ranks:
					ranks[card.rank] = []
				ranks[card.rank].append(card)

		for rank, rank_cards in ranks.items():
			if len(rank_cards) >= 3:
				different_suits = []
				used_suits = set()
				for card in rank_cards:
					if card.suit not in used_suits:
						different_suits.append(card)
						used_suits.add(card.suit)
					if len(different_suits) == 3:
						break

				if len(different_suits) == 3:
					return SET(different_suits)

		return None

	def _try_form_impure_sequence(self, cards: list[CARD]) -> IMPURE_SEQUENCE | None:
		if len(cards) >= 3:
			return IMPURE_SEQUENCE(cards[:3])
		return None
